import dash
from dash import dcc, html, Input, Output
import dash_bootstrap_components as dbc

# 创建简单的测试应用
app = dash.Dash(__name__, external_stylesheets=[dbc.themes.BOOTSTRAP])

# 简单的布局
app.layout = html.Div([
    html.H1("测试应用"),
    html.Div(id="status-area"),
    html.Div([
        dbc.<PERSON><PERSON>("菜单1", id="btn-1", color="light", className="me-2"),
        dbc.<PERSON><PERSON>("菜单2", id="btn-2", color="light", className="me-2"),
        dbc.<PERSON><PERSON>("菜单3", id="btn-3", color="light", className="me-2"),
    ], className="mb-3"),
    html.Div(id="content-area"),
    dcc.Store(id="active-menu", data=""),
])

# 状态显示回调
@app.callback(
    Output("status-area", "children"),
    Input("active-menu", "data")
)
def update_status(active_menu):
    print(f"状态更新: {active_menu}")
    return dbc.Alert(f"当前菜单: {active_menu or '无'}", color="info")

# 菜单点击回调
@app.callback(
    [
        Output("btn-1", "color"),
        Output("btn-2", "color"), 
        Output("btn-3", "color"),
        Output("active-menu", "data")
    ],
    [
        Input("btn-1", "n_clicks"),
        Input("btn-2", "n_clicks"),
        Input("btn-3", "n_clicks")
    ]
)
def update_menu(n1, n2, n3):
    ctx = dash.callback_context
    print(f"菜单点击: {ctx.triggered}")
    
    if not ctx.triggered:
        print("没有触发")
        return "light", "light", "light", ""
    
    button_id = ctx.triggered[0]["prop_id"].split(".")[0]
    print(f"点击的按钮: {button_id}")
    
    colors = ["light", "light", "light"]
    menu_data = ""
    
    if button_id == "btn-1":
        colors[0] = "primary"
        menu_data = "menu1"
    elif button_id == "btn-2":
        colors[1] = "primary"
        menu_data = "menu2"
    elif button_id == "btn-3":
        colors[2] = "primary"
        menu_data = "menu3"
    
    return colors[0], colors[1], colors[2], menu_data

# 内容更新回调
@app.callback(
    Output("content-area", "children"),
    Input("active-menu", "data")
)
def update_content(active_menu):
    print(f"内容更新: {active_menu}")
    
    if not active_menu:
        return html.Div("请选择菜单")
    
    if active_menu == "menu1":
        return html.Div("这是菜单1的内容")
    elif active_menu == "menu2":
        return html.Div("这是菜单2的内容")
    elif active_menu == "menu3":
        return html.Div("这是菜单3的内容")
    
    return html.Div("未知菜单")

if __name__ == "__main__":
    print("启动测试应用...")
    app.run_server(debug=True, port=8051)
