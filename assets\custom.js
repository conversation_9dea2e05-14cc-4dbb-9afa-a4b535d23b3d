// 自定义JavaScript代码，用于改善用户体验

// 全局变量
window.countdownTimer = null

// 当页面加载完成后执行
document.addEventListener('DOMContentLoaded', function () {
  // 启动倒计时功能
  function startCountdown() {
    const estimatedTimeElement = document.getElementById('estimated-time-data')
    const remainingTimeElement = document.getElementById('remaining-time')

    if (estimatedTimeElement && remainingTimeElement) {
      let remainingTime = parseInt(estimatedTimeElement.textContent)

      // 清除之前的计时器
      if (window.countdownTimer) {
        clearInterval(window.countdownTimer)
      }

      // 开始倒计时
      window.countdownTimer = setInterval(function () {
        remainingTime--

        if (remainingTime <= 0) {
          remainingTimeElement.textContent = '即将完成'
          clearInterval(window.countdownTimer)
          window.countdownTimer = null
        } else {
          remainingTimeElement.textContent = remainingTime
        }
      }, 1000)
    }
  }

  // 为批量PIN查询按钮添加点击事件监听器
  function addQueryButtonListener() {
    const pinQueryBtn = document.getElementById('batch-pin-query-btn')
    if (pinQueryBtn) {
      pinQueryBtn.addEventListener('click', function () {
        // 立即显示处理中的状态
        showProcessingState(pinQueryBtn, '处理中...')

        // 延迟启动倒计时，等待进度信息显示
        setTimeout(startCountdown, 1000)
      })
    }

    // 为IP查询按钮添加类似的监听器
    const ipQueryBtn = document.getElementById('ip-location-query-btn')
    if (ipQueryBtn) {
      ipQueryBtn.addEventListener('click', function () {
        showProcessingState(ipQueryBtn, '处理中...')
      })
    }
  }

  // 显示处理中状态的函数
  function showProcessingState(button, text) {
    if (button) {
      button.disabled = true
      button.innerHTML = `
                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                ${text}
            `
    }
  }

  // 初始化按钮监听器
  addQueryButtonListener()

  // 使用MutationObserver监听DOM变化
  const observer = new MutationObserver(function (mutations) {
    mutations.forEach(function (mutation) {
      if (mutation.type === 'childList') {
        addQueryButtonListener()

        // 检查是否有倒计时元素出现
        const estimatedTimeElement = document.getElementById('estimated-time-data')
        const remainingTimeElement = document.getElementById('remaining-time')

        if (estimatedTimeElement && remainingTimeElement && !window.countdownTimer) {
          startCountdown()
        }

        // 检查查询结果是否出现，如果出现则清理倒计时
        const queryResult = document.getElementById('batch-pin-query-result')
        if (queryResult && queryResult.children.length > 0) {
          // 检查是否有Card组件（表示查询完成）
          const hasCard = queryResult.querySelector('[data-dash-is-loading="false"]')
          if (hasCard && window.countdownTimer) {
            console.log('Query completed, clearing countdown timer')
            clearInterval(window.countdownTimer)
            window.countdownTimer = null
          }
        }
      }
    })
  })

  // 开始观察DOM变化
  observer.observe(document.body, {
    childList: true,
    subtree: true,
  })
})

// 为查询按钮添加更好的视觉反馈
function enhanceQueryButtons() {
  const style = document.createElement('style')
  style.textContent = `
        .btn:disabled {
            opacity: 0.8;
            cursor: not-allowed;
        }
        
        .processing-btn {
            background: linear-gradient(45deg, #007bff, #0056b3);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .progress-bar-animated {
            animation: progress-bar-stripes 1s linear infinite;
        }
        
        @keyframes progress-bar-stripes {
            0% { background-position: 1rem 0; }
            100% { background-position: 0 0; }
        }
    `
  document.head.appendChild(style)
}

// 页面加载完成后增强按钮样式
document.addEventListener('DOMContentLoaded', enhanceQueryButtons)
