// 自定义JavaScript代码，用于改善用户体验

// 当页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    
    // 为批量PIN查询按钮添加点击事件监听器
    function addQueryButtonListener() {
        const pinQueryBtn = document.getElementById('batch-pin-query-btn');
        if (pinQueryBtn) {
            pinQueryBtn.addEventListener('click', function() {
                // 立即显示处理中的状态
                showProcessingState(pinQueryBtn, '处理中...');
                
                // 在进度区域显示提示信息
                const progressArea = document.getElementById('batch-pin-progress-area');
                if (progressArea) {
                    progressArea.innerHTML = `
                        <div class="alert alert-info mt-3">
                            <h6 class="alert-heading mb-2">🚀 查询已开始</h6>
                            <p class="mb-1">📊 正在分析文件并准备查询...</p>
                            <p class="mb-0 small">⏳ 系统正在后台处理，请稍候...</p>
                            <hr class="my-2">
                            <div class="progress mb-2" style="height: 20px;">
                                <div class="progress-bar progress-bar-striped progress-bar-animated bg-info" 
                                     role="progressbar" style="width: 100%"></div>
                            </div>
                            <small class="text-muted">系统正在后台处理，请勿关闭页面</small>
                        </div>
                    `;
                }
            });
        }
        
        // 为IP查询按钮添加类似的监听器
        const ipQueryBtn = document.getElementById('ip-location-query-btn');
        if (ipQueryBtn) {
            ipQueryBtn.addEventListener('click', function() {
                showProcessingState(ipQueryBtn, '处理中...');
            });
        }
    }
    
    // 显示处理中状态的函数
    function showProcessingState(button, text) {
        if (button) {
            button.disabled = true;
            button.innerHTML = `
                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                ${text}
            `;
        }
    }
    
    // 初始化按钮监听器
    addQueryButtonListener();
    
    // 使用MutationObserver监听DOM变化，以便为动态添加的按钮添加监听器
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                addQueryButtonListener();
            }
        });
    });
    
    // 开始观察DOM变化
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});

// 为查询按钮添加更好的视觉反馈
function enhanceQueryButtons() {
    const style = document.createElement('style');
    style.textContent = `
        .btn:disabled {
            opacity: 0.8;
            cursor: not-allowed;
        }
        
        .processing-btn {
            background: linear-gradient(45deg, #007bff, #0056b3);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .progress-bar-animated {
            animation: progress-bar-stripes 1s linear infinite;
        }
        
        @keyframes progress-bar-stripes {
            0% { background-position: 1rem 0; }
            100% { background-position: 0 0; }
        }
    `;
    document.head.appendChild(style);
}

// 页面加载完成后增强按钮样式
document.addEventListener('DOMContentLoaded', enhanceQueryButtons);
