# 海狸拾袋管理工具 - 部署指南

## 🚀 快速启动

### 1. 环境检查
确保您的环境已安装所需依赖：
```bash
# 检查Python环境
python --version  # 应该是Python 3.7+

# 激活虚拟环境（如果使用）
# Windows
.venv\Scripts\activate
# Linux/Mac
source .venv/bin/activate
```

### 2. 运行测试
在启动应用前，建议先运行测试确保所有功能正常：
```bash
python test_app.py
```

预期输出：
```
🚀 开始测试海狸拾袋管理工具...
📋 测试: 应用导入 ✅
📋 测试: 查询函数 ✅  
📋 测试: 静态资源 ✅
📊 测试结果: 3/3 通过
🎉 所有测试通过！应用可以正常使用。
```

### 3. 启动应用
```bash
python app.py
```

应用将在默认端口启动，通常是 `http://localhost:8050`

## 📁 文件结构

```
haili_web/
├── app.py                    # 主应用文件
├── query_functions.py        # 查询函数模块
├── config.py                # 配置文件
├── test_app.py              # 测试脚本
├── assets/
│   └── custom.js            # 客户端JavaScript增强
├── query_results/           # 查询结果存储目录（自动创建）
├── OPTIMIZATION_README.md   # 优化说明文档
├── FINAL_FEATURES.md        # 功能说明文档
└── DEPLOYMENT_GUIDE.md      # 本部署指南
```

## ⚙️ 配置说明

### config.py 配置
在使用前，请确保 `config.py` 中的登录配置正确：
```python
LOGIN_CONFIG = {
    "username": "your_username",  # 设置您的用户名
    "password": "your_password",  # 设置您的密码
}
```

### 时间延迟配置
可以调整查询间隔时间：
```python
TIME_SLEEP = 0.5  # 每次查询间隔（秒）
```

## 🎯 功能使用指南

### 批量PIN查询
1. **准备文件**：创建TXT文件，每行一个PIN
2. **上传文件**：在"批量PIN查询手机号"页面上传文件
3. **开始查询**：点击"开始查询"按钮
4. **查看进度**：实时查看处理进度和剩余时间
5. **查看结果**：在"查询结果"标签查看详细数据
6. **下载文件**：在"下载列表"标签下载结果文件

### IP归属地查询
1. **准备文件**：创建CSV文件，每行一个IP地址
2. **上传文件**：在"IP归属地查询"页面上传文件
3. **开始查询**：点击"开始查询"按钮
4. **下载结果**：查询完成后下载结果文件

## 🔧 故障排除

### 常见问题

#### 1. 应用无法启动
- 检查Python环境和依赖包
- 确保端口未被占用
- 查看控制台错误信息

#### 2. 查询功能异常
- 检查网络连接
- 验证登录配置是否正确
- 确保上传的文件格式正确

#### 3. 文件下载问题
- 检查 `query_results` 目录权限
- 确保有足够的磁盘空间
- 验证文件是否生成成功

#### 4. 进度显示异常
- 确保浏览器支持JavaScript
- 检查浏览器控制台是否有错误
- 尝试刷新页面

### 日志查看
应用运行时会在控制台输出日志信息，包括：
- 查询进度
- 错误信息
- 文件保存状态

## 🔒 安全注意事项

1. **配置文件安全**：不要将包含真实密码的config.py提交到版本控制
2. **网络安全**：确保在安全的网络环境中运行
3. **数据保护**：定期清理敏感的查询结果文件
4. **访问控制**：如果部署到服务器，请设置适当的访问控制

## 📊 性能优化

### 查询性能
- 调整 `TIME_SLEEP` 参数平衡速度和稳定性
- 分批处理大量数据
- 监控系统资源使用情况

### 存储管理
- 定期清理 `query_results` 目录
- 设置文件自动清理策略
- 监控磁盘空间使用

## 🆕 版本更新

### 当前版本特性
- ✅ 实时进度显示和倒计时
- ✅ 查询完成后状态自动还原
- ✅ 结果页面标签切换
- ✅ 完善的错误处理
- ✅ 客户端JavaScript增强

### 更新说明
如需更新应用，请：
1. 备份当前配置文件
2. 替换应用文件
3. 运行测试验证功能
4. 恢复配置设置

## 📞 技术支持

如遇到问题，请：
1. 首先运行 `python test_app.py` 进行自诊断
2. 查看控制台错误信息
3. 检查相关文档说明
4. 联系技术支持团队

---

🎉 **祝您使用愉快！**

海狸拾袋管理工具现在提供了完整的用户体验，包括实时进度显示、状态管理和优雅的界面设计。
