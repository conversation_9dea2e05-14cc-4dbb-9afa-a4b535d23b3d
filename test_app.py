#!/usr/bin/env python3
"""
测试应用启动脚本
用于验证所有功能是否正常工作
"""

import sys
import os

def test_import():
    """测试应用导入"""
    try:
        import app
        print("✅ 应用导入成功")
        return True
    except Exception as e:
        print(f"❌ 应用导入失败: {e}")
        return False

def test_query_functions():
    """测试查询函数"""
    try:
        from query_functions import batch_query_phone_by_pin, query_ip_location
        
        # 测试统计功能
        test_content = "test_pin_1\ntest_pin_2\ntest_pin_3"
        
        # 测试PIN查询统计
        result = batch_query_phone_by_pin(None, test_content, return_stats_only=True)
        if result["success"] and result["stats_only"]:
            print("✅ PIN查询统计功能正常")
        else:
            print("❌ PIN查询统计功能异常")
            return False
            
        # 测试IP查询统计
        result = query_ip_location(None, test_content, return_stats_only=True)
        if result["success"] and result["stats_only"]:
            print("✅ IP查询统计功能正常")
        else:
            print("❌ IP查询统计功能异常")
            return False
            
        return True
    except Exception as e:
        print(f"❌ 查询函数测试失败: {e}")
        return False

def test_assets():
    """测试静态资源"""
    try:
        if os.path.exists("assets/custom.js"):
            print("✅ JavaScript文件存在")
            
            # 检查JavaScript文件内容
            with open("assets/custom.js", "r", encoding="utf-8") as f:
                content = f.read()
                if "countdownTimer" in content and "startCountdown" in content:
                    print("✅ JavaScript倒计时功能已实现")
                else:
                    print("❌ JavaScript倒计时功能缺失")
                    return False
        else:
            print("❌ JavaScript文件不存在")
            return False
            
        return True
    except Exception as e:
        print(f"❌ 静态资源测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试海狸拾袋管理工具...")
    print("=" * 50)
    
    tests = [
        ("应用导入", test_import),
        ("查询函数", test_query_functions),
        ("静态资源", test_assets),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} 测试失败")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！应用可以正常使用。")
        print("\n🚀 启动应用:")
        print("python app.py")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
