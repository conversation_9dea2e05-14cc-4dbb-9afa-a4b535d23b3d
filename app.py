import dash
from dash import dcc, html, Input, Output, callback
import dash_bootstrap_components as dbc
from flask_cors import CORS
from flask import send_file
import base64
import os
import glob
from datetime import datetime
from query_functions import (
    login_to_system,
    query_user_auth,
    query_auth_and_coupon,
    query_account_by_pin,
    query_account_by_phone,
    batch_query_phone_by_pin,
    query_ip_location,
    add_merchant_to_whitelist,
)
from config import LOGIN_CONFIG, CORS_CONFIG


# 初始化Dash应用
app = dash.Dash(__name__, external_stylesheets=[dbc.themes.BOOTSTRAP])

# 配置CORS以支持跨域请求
CORS(app.server, resources={r"/*": CORS_CONFIG})

app.title = "海狸拾袋管理工具"

# 配置服务器以支持跨域
app.server.config["CORS_HEADERS"] = "Content-Type"

# 定义菜单项
MENU_ITEMS = [
    {"id": "user-auth-query", "label": "查询用户实名认证", "icon": "fas fa-id-card"},
    {
        "id": "auth-coupon-query",
        "label": "查询实名认证和优惠券",
        "icon": "fas fa-search",
    },
    {"id": "pin-to-account", "label": "PIN查询账号信息", "icon": "fas fa-user-search"},
    {"id": "phone-to-account", "label": "手机号查询账号", "icon": "fas fa-mobile-alt"},
    {"id": "batch-pin-query", "label": "批量PIN查询手机号", "icon": "fas fa-list"},
    {
        "id": "ip-location-query",
        "label": "IP归属地查询",
        "icon": "fas fa-map-marker-alt",
    },
    {"id": "coupon-whitelist", "label": "消费券商家白名单", "icon": "fas fa-store"},
]

# 侧边栏样式
SIDEBAR_STYLE = {
    "position": "fixed",
    "top": 0,
    "left": 0,
    "bottom": 0,
    "width": "16rem",
    "padding": "2rem 1rem",
    "background-color": "#f8f9fa",
    "border-right": "1px solid #dee2e6",
}

# 主内容区域样式
CONTENT_STYLE = {
    "margin-left": "18rem",
    "margin-right": "2rem",
    "padding": "2rem 1rem",
}


# 创建侧边栏
def create_sidebar():
    return html.Div(
        [
            html.H2("管理工具", className="display-6 mb-4"),
            html.Hr(),
            dbc.Nav(
                [
                    dbc.NavLink(
                        [html.I(className=f"{item['icon']} me-2"), item["label"]],
                        href="#",
                        id=f"nav-{item['id']}",
                        active=False,
                        className="mb-2",
                    )
                    for item in MENU_ITEMS
                ],
                vertical=True,
                pills=True,
            ),
        ],
        style=SIDEBAR_STYLE,
    )


# 创建主内容区域
def create_content():
    return html.Div(
        id="page-content",
        style=CONTENT_STYLE,
        children=[
            html.H3("海狸拾袋管理工具", className="mb-3", style={"font-size": "24px"}),
            html.P(
                "请从左侧菜单选择功能模块",
                className="text-muted mb-3",
                style={"font-size": "16px"},
            ),
            # 登录状态区域
            html.Div(id="login-status-area"),
            html.Div(id="main-content"),
        ],
    )


# 全局session变量
GLOBAL_SESSION = None
LOGIN_STATUS = {"success": False, "message": "未初始化"}


# 获取最近的查询结果文件
def get_recent_files(file_type="all", limit=10):
    """
    获取最近的查询结果文件

    参数:
        file_type: 文件类型 ("pin", "ip", "all")
        limit: 返回文件数量限制

    返回:
        list: 文件信息列表
    """
    try:
        output_dir = "query_results"
        if not os.path.exists(output_dir):
            return []

        # 根据文件类型获取文件
        if file_type == "pin":
            pattern = os.path.join(output_dir, "pin_*.txt")
        elif file_type == "ip":
            pattern = os.path.join(output_dir, "ip_*.txt")
        else:
            pattern = os.path.join(output_dir, "*.txt")

        files = glob.glob(pattern)

        # 按修改时间排序，最新的在前
        files.sort(key=lambda x: os.path.getmtime(x), reverse=True)

        # 限制返回数量
        files = files[:limit]

        # 构建文件信息
        file_info = []
        for file_path in files:
            filename = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))

            file_info.append(
                {
                    "filename": filename,
                    "filepath": file_path,
                    "size": file_size,
                    "modified": mod_time.strftime("%Y-%m-%d %H:%M:%S"),
                    "type": "PIN查询" if filename.startswith("pin_") else "IP查询",
                }
            )

        return file_info

    except Exception as e:
        print(f"获取文件列表失败: {str(e)}")
        return []


# 应用启动时自动登录
def auto_login():
    """应用启动时自动登录"""
    global GLOBAL_SESSION, LOGIN_STATUS
    try:
        # 检查配置是否为空
        if not LOGIN_CONFIG["username"] or not LOGIN_CONFIG["password"]:
            GLOBAL_SESSION = None
            LOGIN_STATUS = {
                "success": False,
                "message": "请在config.py中配置正确的用户名和密码",
            }
            return LOGIN_STATUS

        result = login_to_system(LOGIN_CONFIG["username"], LOGIN_CONFIG["password"])
        if result["success"]:
            GLOBAL_SESSION = result["session"]
            LOGIN_STATUS = {"success": True, "message": "登录成功"}
        else:
            GLOBAL_SESSION = None
            LOGIN_STATUS = {"success": False, "message": result["message"]}
        return LOGIN_STATUS
    except Exception as e:
        GLOBAL_SESSION = None
        LOGIN_STATUS = {"success": False, "message": f"自动登录失败: {str(e)}"}
        return LOGIN_STATUS


# 应用布局
app.layout = html.Div(
    [
        dcc.Store(id="active-menu", data=""),
        dcc.Store(id="login-session", data=auto_login()),  # 启动时自动登录
        dcc.Download(id="download-data"),  # 文件下载组件
        create_sidebar(),
        create_content(),
    ]
)


# 菜单点击回调
@app.callback(
    [Output(f"nav-{item['id']}", "active") for item in MENU_ITEMS]
    + [Output("active-menu", "data")],
    [Input(f"nav-{item['id']}", "n_clicks") for item in MENU_ITEMS],
)
def update_active_menu(*args):
    ctx = dash.callback_context
    if not ctx.triggered:
        return [False] * len(MENU_ITEMS) + [""]

    button_id = ctx.triggered[0]["prop_id"].split(".")[0]
    menu_id = button_id.replace("nav-", "")

    # 设置活跃状态
    active_states = [button_id == f"nav-{item['id']}" for item in MENU_ITEMS]

    return active_states + [menu_id]


# 主内容更新回调
@app.callback(Output("main-content", "children"), Input("active-menu", "data"))
def update_main_content(active_menu):
    if not active_menu:
        return html.Div(
            [
                html.H3("请选择功能", className="text-muted"),
                html.P("从左侧菜单中选择您需要使用的功能模块"),
            ]
        )

    # 根据选择的菜单返回对应内容
    if active_menu == "user-auth-query":
        return create_user_auth_query_page()
    elif active_menu == "auth-coupon-query":
        return create_auth_coupon_query_page()
    elif active_menu == "pin-to-account":
        return create_pin_to_account_page()
    elif active_menu == "phone-to-account":
        return create_phone_to_account_page()
    elif active_menu == "batch-pin-query":
        return create_batch_pin_query_page()
    elif active_menu == "ip-location-query":
        return create_ip_location_query_page()
    elif active_menu == "coupon-whitelist":
        return create_coupon_whitelist_page()
    else:
        return html.Div("功能开发中...")


# 查询用户实名认证页面
def create_user_auth_query_page():
    return html.Div(
        [
            html.H5("查询用户实名认证", className="mb-3"),
            dbc.Card(
                [
                    dbc.CardBody(
                        [
                            dbc.Row(
                                [
                                    dbc.Col(
                                        [
                                            dbc.Label(
                                                "姓名",
                                                html_for="auth-name-input",
                                                style={"font-size": "14px"},
                                            ),
                                            dbc.Input(
                                                id="auth-name-input",
                                                type="text",
                                                placeholder="请输入真实姓名",
                                                size="sm",
                                                className="mb-2",
                                            ),
                                        ],
                                        width=12,
                                    ),
                                ]
                            ),
                            dbc.Row(
                                [
                                    dbc.Col(
                                        [
                                            dbc.Label(
                                                "手机号",
                                                html_for="auth-phone-input",
                                                style={"font-size": "14px"},
                                            ),
                                            dbc.Input(
                                                id="auth-phone-input",
                                                type="tel",
                                                placeholder="请输入手机号",
                                                size="sm",
                                                className="mb-2",
                                            ),
                                        ],
                                        width=12,
                                    ),
                                ]
                            ),
                            dbc.Row(
                                [
                                    dbc.Col(
                                        [
                                            dbc.Label(
                                                "身份证号",
                                                html_for="auth-idcard-input",
                                                style={"font-size": "14px"},
                                            ),
                                            dbc.Input(
                                                id="auth-idcard-input",
                                                type="text",
                                                placeholder="请输入身份证号",
                                                size="sm",
                                                className="mb-2",
                                            ),
                                        ],
                                        width=12,
                                    ),
                                ]
                            ),
                            dbc.Row(
                                [
                                    dbc.Col(
                                        [
                                            dbc.Button(
                                                "查询",
                                                id="auth-query-btn",
                                                color="primary",
                                                size="sm",
                                                className="me-2",
                                            ),
                                            dbc.Button(
                                                "清空",
                                                id="auth-clear-btn",
                                                color="secondary",
                                                outline=True,
                                                size="sm",
                                            ),
                                        ],
                                        width=12,
                                        className="text-center mt-2",
                                    )
                                ]
                            ),
                        ]
                    )
                ],
                className="mb-3",
            ),
            # 查询结果区域
            html.Div(id="auth-query-result"),
        ]
    )


# 查询实名认证和优惠券情况页面
def create_auth_coupon_query_page():
    return html.Div(
        [
            html.H5("查询实名认证和优惠券情况", className="mb-3"),
            dbc.Card(
                [
                    dbc.CardBody(
                        [
                            dbc.Row(
                                [
                                    dbc.Col(
                                        [
                                            dbc.Label(
                                                "姓名",
                                                html_for="auth-coupon-name-input",
                                                style={"font-size": "14px"},
                                            ),
                                            dbc.Input(
                                                id="auth-coupon-name-input",
                                                type="text",
                                                placeholder="请输入真实姓名",
                                                size="sm",
                                                className="mb-2",
                                            ),
                                        ],
                                        width=12,
                                    ),
                                ]
                            ),
                            dbc.Row(
                                [
                                    dbc.Col(
                                        [
                                            dbc.Label(
                                                "手机号",
                                                html_for="auth-coupon-phone-input",
                                                style={"font-size": "14px"},
                                            ),
                                            dbc.Input(
                                                id="auth-coupon-phone-input",
                                                type="tel",
                                                placeholder="请输入手机号",
                                                size="sm",
                                                className="mb-2",
                                            ),
                                        ],
                                        width=12,
                                    ),
                                ]
                            ),
                            dbc.Row(
                                [
                                    dbc.Col(
                                        [
                                            dbc.Label(
                                                "身份证号",
                                                html_for="auth-coupon-idcard-input",
                                                style={"font-size": "14px"},
                                            ),
                                            dbc.Input(
                                                id="auth-coupon-idcard-input",
                                                type="text",
                                                placeholder="请输入身份证号",
                                                size="sm",
                                                className="mb-2",
                                            ),
                                        ],
                                        width=12,
                                    ),
                                ]
                            ),
                            dbc.Row(
                                [
                                    dbc.Col(
                                        [
                                            dbc.Button(
                                                "查询",
                                                id="auth-coupon-query-btn",
                                                color="primary",
                                                size="sm",
                                                className="me-2",
                                            ),
                                            dbc.Button(
                                                "清空",
                                                id="auth-coupon-clear-btn",
                                                color="secondary",
                                                outline=True,
                                                size="sm",
                                            ),
                                        ],
                                        width=12,
                                        className="text-center mt-2",
                                    )
                                ]
                            ),
                        ]
                    )
                ],
                className="mb-3",
            ),
            # 查询结果区域
            html.Div(id="auth-coupon-query-result"),
        ]
    )


# 用户PIN查账号页面
def create_pin_to_account_page():
    return html.Div(
        [
            html.H5("PIN查询账号信息", className="mb-3"),
            dbc.Card(
                [
                    dbc.CardBody(
                        [
                            dbc.Row(
                                [
                                    dbc.Col(
                                        [
                                            dbc.Label(
                                                "用户PIN",
                                                html_for="pin-input",
                                                style={"font-size": "14px"},
                                            ),
                                            dbc.Input(
                                                id="pin-input",
                                                type="text",
                                                placeholder="请输入用户PIN",
                                                size="sm",
                                                className="mb-2",
                                            ),
                                        ],
                                        width=12,
                                    ),
                                ]
                            ),
                            dbc.Row(
                                [
                                    dbc.Col(
                                        [
                                            dbc.Button(
                                                "查询",
                                                id="pin-query-btn",
                                                color="primary",
                                                size="sm",
                                                className="me-2",
                                            ),
                                            dbc.Button(
                                                "清空",
                                                id="pin-clear-btn",
                                                color="secondary",
                                                outline=True,
                                                size="sm",
                                            ),
                                        ],
                                        width=12,
                                        className="text-center mt-2",
                                    )
                                ]
                            ),
                        ]
                    )
                ],
                className="mb-3",
            ),
            # 查询结果区域
            html.Div(id="pin-query-result"),
        ]
    )


# 手机号查询账号页面
def create_phone_to_account_page():
    return html.Div(
        [
            html.H5("手机号查询账号", className="mb-3"),
            dbc.Card(
                [
                    dbc.CardBody(
                        [
                            dbc.Row(
                                [
                                    dbc.Col(
                                        [
                                            dbc.Label(
                                                "手机号",
                                                html_for="phone-input",
                                                style={"font-size": "14px"},
                                            ),
                                            dbc.Input(
                                                id="phone-input",
                                                type="tel",
                                                placeholder="请输入手机号",
                                                size="sm",
                                                className="mb-2",
                                            ),
                                        ],
                                        width=12,
                                    ),
                                ]
                            ),
                            dbc.Row(
                                [
                                    dbc.Col(
                                        [
                                            dbc.Button(
                                                "查询",
                                                id="phone-query-btn",
                                                color="primary",
                                                size="sm",
                                                className="me-2",
                                            ),
                                            dbc.Button(
                                                "清空",
                                                id="phone-clear-btn",
                                                color="secondary",
                                                outline=True,
                                                size="sm",
                                            ),
                                        ],
                                        width=12,
                                        className="text-center mt-2",
                                    )
                                ]
                            ),
                        ]
                    )
                ],
                className="mb-3",
            ),
            # 查询结果区域
            html.Div(id="phone-query-result"),
        ]
    )


# 批量PIN查询手机号页面
def create_batch_pin_query_page():
    return html.Div(
        [
            html.H5("批量PIN查询手机号", className="mb-3"),
            dbc.Card(
                [
                    dbc.CardBody(
                        [
                            dbc.Row(
                                [
                                    dbc.Col(
                                        [
                                            dbc.Label(
                                                "上传TXT文件",
                                                style={"font-size": "14px"},
                                            ),
                                            dcc.Upload(
                                                id="batch-pin-upload",
                                                children=html.Div(
                                                    [
                                                        "拖拽文件到此处或 ",
                                                        html.A("点击选择文件"),
                                                    ]
                                                ),
                                                style={
                                                    "width": "100%",
                                                    "height": "60px",
                                                    "lineHeight": "60px",
                                                    "borderWidth": "1px",
                                                    "borderStyle": "dashed",
                                                    "borderRadius": "5px",
                                                    "textAlign": "center",
                                                    "margin": "10px",
                                                },
                                                multiple=False,
                                                className="mb-2",
                                            ),
                                        ],
                                        width=12,
                                    ),
                                ]
                            ),
                            dbc.Row(
                                [
                                    dbc.Col(
                                        [
                                            dbc.Button(
                                                "开始查询",
                                                id="batch-pin-query-btn",
                                                color="primary",
                                                size="sm",
                                                className="me-2",
                                                disabled=True,
                                            ),
                                            dbc.Button(
                                                "查看下载列表",
                                                id="batch-pin-download-btn",
                                                color="success",
                                                size="sm",
                                            ),
                                        ],
                                        width=12,
                                        className="text-center mt-2",
                                    )
                                ]
                            ),
                        ]
                    )
                ],
                className="mb-3",
            ),
            # 进度条区域
            html.Div(id="batch-pin-progress-area", className="mt-3"),
            # 查询结果区域
            html.Div(id="batch-pin-query-result"),
            # 隐藏的Interval组件用于进度更新
            dcc.Interval(
                id="batch-pin-interval", interval=1000, n_intervals=0, disabled=True
            ),
            # 存储查询状态
            dcc.Store(
                id="batch-pin-status",
                data={"running": False, "total": 0, "current": 0, "start_time": None},
            ),
        ]
    )


# IP归属地查询页面
def create_ip_location_query_page():
    return html.Div(
        [
            html.H5("IP归属地查询", className="mb-3"),
            dbc.Card(
                [
                    dbc.CardBody(
                        [
                            dbc.Row(
                                [
                                    dbc.Col(
                                        [
                                            dbc.Label(
                                                "上传CSV文件",
                                                style={"font-size": "14px"},
                                            ),
                                            dcc.Upload(
                                                id="ip-location-upload",
                                                children=html.Div(
                                                    [
                                                        "拖拽文件到此处或 ",
                                                        html.A("点击选择文件"),
                                                    ]
                                                ),
                                                style={
                                                    "width": "100%",
                                                    "height": "60px",
                                                    "lineHeight": "60px",
                                                    "borderWidth": "1px",
                                                    "borderStyle": "dashed",
                                                    "borderRadius": "5px",
                                                    "textAlign": "center",
                                                    "margin": "10px",
                                                },
                                                multiple=False,
                                                className="mb-2",
                                            ),
                                        ],
                                        width=12,
                                    ),
                                ]
                            ),
                            dbc.Row(
                                [
                                    dbc.Col(
                                        [
                                            dbc.Button(
                                                "开始查询",
                                                id="ip-location-query-btn",
                                                color="primary",
                                                size="sm",
                                                className="me-2",
                                                disabled=True,
                                            ),
                                            dbc.Button(
                                                "查看下载列表",
                                                id="ip-location-download-btn",
                                                color="success",
                                                size="sm",
                                            ),
                                        ],
                                        width=12,
                                        className="text-center mt-2",
                                    )
                                ]
                            ),
                        ]
                    )
                ],
                className="mb-3",
            ),
            # 查询结果区域
            html.Div(id="ip-location-query-result"),
        ]
    )


# 消费券添加商家白名单页面
def create_coupon_whitelist_page():
    return html.Div(
        [
            html.H5("消费券商家白名单", className="mb-3"),
            dbc.Card(
                [
                    dbc.CardBody(
                        [
                            html.P(
                                "功能详细内容待后续开发",
                                className="card-text text-muted",
                            )
                        ]
                    )
                ],
                className="mb-3",
            ),
        ]
    )


# 查询用户实名认证回调函数
@app.callback(
    Output("auth-query-result", "children"),
    [Input("auth-query-btn", "n_clicks")],
    [
        dash.dependencies.State("auth-name-input", "value"),
        dash.dependencies.State("auth-phone-input", "value"),
        dash.dependencies.State("auth-idcard-input", "value"),
        dash.dependencies.State("login-session", "data"),
    ],
)
def query_user_auth_callback(n_clicks, name, phone, idcard, session_data):
    if n_clicks is None:
        return ""

    if not all([name, phone, idcard]):
        return dbc.Alert(
            "请填写完整的用户信息（姓名、手机号、身份证号）",
            color="warning",
            className="mt-3",
        )

    # 检查登录状态
    if not GLOBAL_SESSION or not LOGIN_STATUS.get("success"):
        return dbc.Alert("系统未登录，请检查配置", color="warning", className="mt-3")

    # 调用实际的查询函数
    try:
        result = query_user_auth(GLOBAL_SESSION, name, phone, idcard)
        if result["success"]:
            return dbc.Card(
                [
                    dbc.CardHeader([html.H5("查询结果", className="mb-0")]),
                    dbc.CardBody(
                        [
                            html.Pre(
                                result.get("raw_response", "无响应内容"),
                                style={
                                    "white-space": "pre-wrap",
                                    "word-wrap": "break-word",
                                },
                            )
                        ]
                    ),
                ],
                className="mt-3",
            )
        else:
            return dbc.Alert(
                f"查询失败: {result['message']}", color="danger", className="mt-3"
            )
    except Exception as e:
        return dbc.Alert(f"查询出错: {str(e)}", color="danger", className="mt-3")


# 清空用户实名认证输入回调函数
@app.callback(
    [
        Output("auth-name-input", "value"),
        Output("auth-phone-input", "value"),
        Output("auth-idcard-input", "value"),
    ],
    [Input("auth-clear-btn", "n_clicks")],
)
def clear_auth_inputs(n_clicks):
    if n_clicks is None:
        return "", "", ""
    return "", "", ""


# 查询实名认证和优惠券情况回调函数
@app.callback(
    Output("auth-coupon-query-result", "children"),
    [Input("auth-coupon-query-btn", "n_clicks")],
    [
        dash.dependencies.State("auth-coupon-name-input", "value"),
        dash.dependencies.State("auth-coupon-phone-input", "value"),
        dash.dependencies.State("auth-coupon-idcard-input", "value"),
        dash.dependencies.State("login-session", "data"),
    ],
)
def query_auth_coupon(n_clicks, name, phone, idcard, session_data):
    if n_clicks is None:
        return ""

    if not all([name, phone, idcard]):
        return dbc.Alert(
            "请填写完整的用户信息（姓名、手机号、身份证号）",
            color="warning",
            className="mt-3",
        )

    # 检查登录状态
    if not GLOBAL_SESSION or not LOGIN_STATUS.get("success"):
        return dbc.Alert("系统未登录，请检查配置", color="warning", className="mt-3")

    # 调用实际的查询函数
    try:
        result = query_auth_and_coupon(GLOBAL_SESSION, name, phone, idcard)

        if result["success"]:
            return dbc.Card(
                [
                    dbc.CardHeader([html.H5("查询结果", className="mb-0")]),
                    dbc.CardBody(
                        [
                            html.Pre(
                                result.get("raw_response", "无响应内容"),
                                style={
                                    "white-space": "pre-wrap",
                                    "word-wrap": "break-word",
                                },
                            )
                        ]
                    ),
                ],
                className="mt-3",
            )
        else:
            return dbc.Alert(
                f"查询失败: {result['message']}", color="danger", className="mt-3"
            )
    except Exception as e:
        return dbc.Alert(f"查询出错: {str(e)}", color="danger", className="mt-3")


# 清空实名认证和优惠券查询输入回调函数
@app.callback(
    [
        Output("auth-coupon-name-input", "value"),
        Output("auth-coupon-phone-input", "value"),
        Output("auth-coupon-idcard-input", "value"),
    ],
    [Input("auth-coupon-clear-btn", "n_clicks")],
)
def clear_auth_coupon_inputs(n_clicks):
    if n_clicks is None:
        return "", "", ""
    return "", "", ""


# PIN查询账号回调函数
@app.callback(
    Output("pin-query-result", "children"),
    [Input("pin-query-btn", "n_clicks")],
    [
        dash.dependencies.State("pin-input", "value"),
        dash.dependencies.State("login-session", "data"),
    ],
)
def query_pin_account(n_clicks, pin, session_data):
    if n_clicks is None:
        return ""

    if not pin:
        return dbc.Alert("请输入用户PIN", color="warning", className="mt-3")

    # 检查登录状态
    if not GLOBAL_SESSION or not LOGIN_STATUS.get("success"):
        return dbc.Alert("系统未登录，请检查配置", color="warning", className="mt-3")

    # 调用实际的查询函数
    try:
        result = query_account_by_pin(GLOBAL_SESSION, pin)

        if result["success"]:
            return dbc.Card(
                [
                    dbc.CardHeader([html.H5("查询结果", className="mb-0")]),
                    dbc.CardBody(
                        [
                            html.Pre(
                                result.get("raw_response", "无响应内容"),
                                style={
                                    "white-space": "pre-wrap",
                                    "word-wrap": "break-word",
                                },
                            )
                        ]
                    ),
                ],
                className="mt-3",
            )
        else:
            return dbc.Alert(
                f"查询失败: {result['message']}", color="danger", className="mt-3"
            )
    except Exception as e:
        return dbc.Alert(f"查询出错: {str(e)}", color="danger", className="mt-3")


# 清空PIN输入回调函数
@app.callback(Output("pin-input", "value"), [Input("pin-clear-btn", "n_clicks")])
def clear_pin_input(n_clicks):
    if n_clicks is None:
        return ""
    return ""


# 手机号查询账号回调函数
@app.callback(
    Output("phone-query-result", "children"),
    [Input("phone-query-btn", "n_clicks")],
    [
        dash.dependencies.State("phone-input", "value"),
        dash.dependencies.State("login-session", "data"),
    ],
)
def query_phone_account(n_clicks, phone, session_data):
    if n_clicks is None:
        return ""

    if not phone:
        return dbc.Alert("请输入手机号", color="warning", className="mt-3")

    # 检查登录状态
    if not GLOBAL_SESSION or not LOGIN_STATUS.get("success"):
        return dbc.Alert("系统未登录，请检查配置", color="warning", className="mt-3")

    # 调用实际的查询函数
    try:
        result = query_account_by_phone(GLOBAL_SESSION, phone)

        if result["success"]:
            return dbc.Card(
                [
                    dbc.CardHeader([html.H5("查询结果", className="mb-0")]),
                    dbc.CardBody(
                        [
                            html.Pre(
                                result.get("raw_response", "无响应内容"),
                                style={
                                    "white-space": "pre-wrap",
                                    "word-wrap": "break-word",
                                },
                            )
                        ]
                    ),
                ],
                className="mt-3",
            )
        else:
            return dbc.Alert(
                f"查询失败: {result['message']}", color="danger", className="mt-3"
            )
    except Exception as e:
        return dbc.Alert(f"查询出错: {str(e)}", color="danger", className="mt-3")


# 清空手机号输入回调函数
@app.callback(Output("phone-input", "value"), [Input("phone-clear-btn", "n_clicks")])
def clear_phone_input(n_clicks):
    if n_clicks is None:
        return ""
    return ""


# 批量PIN查询文件上传回调
@app.callback(
    [
        Output("batch-pin-query-btn", "disabled"),
        Output("batch-pin-query-result", "children"),
    ],
    [Input("batch-pin-upload", "contents")],
    [dash.dependencies.State("batch-pin-upload", "filename")],
)
def handle_batch_pin_upload(contents, filename):
    if contents is None:
        return True, ""

    try:
        # 解析上传的文件内容
        content_type, content_string = contents.split(",")
        decoded = base64.b64decode(content_string)

        # 检查文件类型
        if not filename.lower().endswith(".txt"):
            return True, dbc.Alert("请上传TXT文件", color="warning", className="mt-3")

        # 解码文件内容
        file_content = decoded.decode("utf-8")

        return False, dbc.Alert(
            f"文件 {filename} 上传成功，包含 {len([line for line in file_content.split('\\n') if line.strip()])} 个PIN",
            color="success",
            className="mt-3",
        )

    except Exception as e:
        return True, dbc.Alert(
            f"文件上传失败: {str(e)}", color="danger", className="mt-3"
        )


# 批量PIN查询下载列表回调
@app.callback(
    Output("batch-pin-query-result", "children", allow_duplicate=True),
    [Input("batch-pin-download-btn", "n_clicks")],
    prevent_initial_call=True,
)
def show_pin_download_list(n_clicks):
    if n_clicks is None:
        return dash.no_update

    files = get_recent_files("pin", 10)

    if not files:
        return dbc.Alert("暂无查询结果文件", color="info", className="mt-3")

    # 构建文件列表
    file_list = []
    for file_info in files:
        file_list.append(
            dbc.ListGroupItem(
                [
                    html.Div(
                        [
                            html.H6(file_info["filename"], className="mb-1"),
                            html.P(
                                f"类型: {file_info['type']} | 大小: {file_info['size']} 字节 | 时间: {file_info['modified']}",
                                className="mb-1 text-muted small",
                            ),
                            dbc.Button(
                                "下载",
                                id={
                                    "type": "download-btn",
                                    "filename": file_info["filename"],
                                },
                                color="primary",
                                size="sm",
                            ),
                        ]
                    )
                ]
            )
        )

    return dbc.Card(
        [
            dbc.CardHeader([html.H5("最近的PIN查询结果文件", className="mb-0")]),
            dbc.CardBody([dbc.ListGroup(file_list, flush=True)]),
        ],
        className="mt-3",
    )


# 批量PIN查询执行回调
@app.callback(
    [
        Output("batch-pin-query-result", "children", allow_duplicate=True),
        Output("batch-pin-progress-area", "children", allow_duplicate=True),
        Output("batch-pin-query-btn", "children", allow_duplicate=True),
        Output("batch-pin-query-btn", "disabled", allow_duplicate=True),
    ],
    [Input("batch-pin-query-btn", "n_clicks")],
    [
        dash.dependencies.State("batch-pin-upload", "contents"),
        dash.dependencies.State("login-session", "data"),
    ],
    prevent_initial_call=True,
)
def execute_batch_pin_query(n_clicks, contents, session_data):
    if n_clicks is None or contents is None:
        return dash.no_update, "", "开始查询", False

    # 检查登录状态
    if not GLOBAL_SESSION or not LOGIN_STATUS.get("success"):
        return (
            dbc.Alert("系统未登录，请检查配置", color="warning", className="mt-3"),
            "",
            "开始查询",
            False,
        )

    try:
        # 解析上传的文件内容
        _, content_string = contents.split(",")
        decoded = base64.b64decode(content_string)
        file_content = decoded.decode("utf-8")

        # 第一步：获取统计信息
        stats_result = batch_query_phone_by_pin(
            GLOBAL_SESSION, file_content, return_stats_only=True
        )

        if not stats_result["success"]:
            return (
                dbc.Alert(
                    f"文件处理失败: {stats_result['message']}",
                    color="danger",
                    className="mt-3",
                ),
                "",
                "开始查询",
                False,
            )

        # 第二步：执行实际查询
        # 显示开始查询的进度信息
        progress_info = dbc.Alert(
            [
                html.H6("🚀 查询已开始", className="alert-heading mb-2"),
                html.P(f"📊 {stats_result['message']}", className="mb-1"),
                html.P("⏳ 正在后台处理中，请稍候...", className="mb-0 small"),
                html.Hr(className="my-2"),
                dbc.Progress(
                    value=100,
                    animated=True,
                    striped=True,
                    color="info",
                    className="mb-2",
                    style={"height": "20px"},
                ),
                html.Small("系统正在后台处理，请勿关闭页面", className="text-muted"),
            ],
            color="info",
            className="mt-3",
        )

        # 执行实际查询
        result = batch_query_phone_by_pin(GLOBAL_SESSION, file_content)

        if result["success"]:
            # 创建结果页面的切换标签
            result_tabs = dbc.Tabs(
                [
                    dbc.Tab(
                        label="查询结果",
                        tab_id="result-tab",
                        children=[
                            dbc.Alert(
                                [
                                    html.H6(
                                        "✅ 查询完成！",
                                        className="alert-heading mb-2",
                                    ),
                                    html.P(
                                        f"📊 总计处理 {stats_result['total_count']} 个PIN，预估用时 {stats_result['estimated_time']:.1f} 秒",
                                        className="mb-1",
                                    ),
                                    html.P(f"📄 {result['message']}", className="mb-0"),
                                ],
                                color="success",
                                className="mb-2 mt-3",
                            ),
                            html.Pre(
                                result.get("raw_response", "无响应内容"),
                                style={
                                    "white-space": "pre-wrap",
                                    "word-wrap": "break-word",
                                    "max-height": "400px",
                                    "overflow-y": "auto",
                                },
                            ),
                        ],
                    ),
                    dbc.Tab(
                        label="下载列表",
                        tab_id="download-tab",
                        children=[
                            html.Div(id="download-list-content", className="mt-3")
                        ],
                    ),
                ],
                id="result-tabs",
                active_tab="result-tab",
                className="mt-3",
            )

            return (
                dbc.Card(
                    [
                        dbc.CardHeader([html.H5("批量PIN查询结果", className="mb-0")]),
                        dbc.CardBody([result_tabs]),
                    ],
                    className="mt-3",
                ),
                "",  # 清空进度区域
                "开始查询",  # 恢复按钮文本
                False,  # 启用按钮
            )
        else:
            return (
                dbc.Alert(
                    f"批量查询失败: {result['message']}",
                    color="danger",
                    className="mt-3",
                ),
                "",
                "开始查询",
                False,
            )

    except Exception as e:
        return (
            dbc.Alert(f"批量查询出错: {str(e)}", color="danger", className="mt-3"),
            "",
            "开始查询",
            False,
        )


# 下载列表标签内容回调
@app.callback(
    Output("download-list-content", "children"),
    [Input("result-tabs", "active_tab")],
    prevent_initial_call=True,
)
def load_download_list_content(active_tab):
    if active_tab == "download-tab":
        files = get_recent_files("pin", 10)

        if not files:
            return dbc.Alert("暂无查询结果文件", color="info", className="mt-3")

        # 构建文件列表
        file_list = []
        for file_info in files:
            file_list.append(
                dbc.ListGroupItem(
                    [
                        html.Div(
                            [
                                html.H6(file_info["filename"], className="mb-1"),
                                html.P(
                                    f"类型: {file_info['type']} | 大小: {file_info['size']} 字节 | 时间: {file_info['modified']}",
                                    className="mb-1 text-muted small",
                                ),
                                dbc.Button(
                                    "下载",
                                    id={
                                        "type": "download-btn",
                                        "filename": file_info["filename"],
                                    },
                                    color="primary",
                                    size="sm",
                                ),
                            ]
                        )
                    ]
                )
            )

        return dbc.ListGroup(file_list, flush=True)

    return ""


# IP查询文件上传回调
@app.callback(
    [
        Output("ip-location-query-btn", "disabled"),
        Output("ip-location-query-result", "children"),
    ],
    [Input("ip-location-upload", "contents")],
    [dash.dependencies.State("ip-location-upload", "filename")],
)
def handle_ip_location_upload(contents, filename):
    if contents is None:
        return True, ""

    try:
        # 解析上传的文件内容
        content_type, content_string = contents.split(",")
        decoded = base64.b64decode(content_string)

        # 检查文件类型
        if not filename.lower().endswith(".csv"):
            return True, dbc.Alert("请上传CSV文件", color="warning", className="mt-3")

        # 解码文件内容
        file_content = decoded.decode("utf-8")

        return False, dbc.Alert(
            f"文件 {filename} 上传成功，包含 {len([line for line in file_content.split('\\n') if line.strip()])} 行数据",
            color="success",
            className="mt-3",
        )

    except Exception as e:
        return True, dbc.Alert(
            f"文件上传失败: {str(e)}", color="danger", className="mt-3"
        )


# IP查询下载列表回调
@app.callback(
    Output("ip-location-query-result", "children", allow_duplicate=True),
    [Input("ip-location-download-btn", "n_clicks")],
    prevent_initial_call=True,
)
def show_ip_download_list(n_clicks):
    if n_clicks is None:
        return dash.no_update

    files = get_recent_files("ip", 10)

    if not files:
        return dbc.Alert("暂无查询结果文件", color="info", className="mt-3")

    # 构建文件列表
    file_list = []
    for file_info in files:
        file_list.append(
            dbc.ListGroupItem(
                [
                    html.Div(
                        [
                            html.H6(file_info["filename"], className="mb-1"),
                            html.P(
                                f"类型: {file_info['type']} | 大小: {file_info['size']} 字节 | 时间: {file_info['modified']}",
                                className="mb-1 text-muted small",
                            ),
                            dbc.Button(
                                "下载",
                                id={
                                    "type": "download-btn",
                                    "filename": file_info["filename"],
                                },
                                color="primary",
                                size="sm",
                            ),
                        ]
                    )
                ]
            )
        )

    return dbc.Card(
        [
            dbc.CardHeader([html.H5("最近的IP查询结果文件", className="mb-0")]),
            dbc.CardBody([dbc.ListGroup(file_list, flush=True)]),
        ],
        className="mt-3",
    )


# IP查询执行回调
@app.callback(
    [
        Output("ip-location-query-result", "children", allow_duplicate=True),
        Output("ip-location-query-btn", "disabled", allow_duplicate=True),
    ],
    [Input("ip-location-query-btn", "n_clicks")],
    [
        dash.dependencies.State("ip-location-upload", "contents"),
        dash.dependencies.State("login-session", "data"),
    ],
    prevent_initial_call=True,
)
def execute_ip_location_query(n_clicks, contents, session_data):
    if n_clicks is None or contents is None:
        return dash.no_update, False

    # 检查登录状态
    if not GLOBAL_SESSION or not LOGIN_STATUS.get("success"):
        return (
            dbc.Alert("系统未登录，请检查配置", color="warning", className="mt-3"),
            False,
        )

    try:
        # 解析上传的文件内容
        _, content_string = contents.split(",")
        decoded = base64.b64decode(content_string)
        file_content = decoded.decode("utf-8")

        # 第一步：获取统计信息
        stats_result = query_ip_location(
            GLOBAL_SESSION, file_content, return_stats_only=True
        )

        if not stats_result["success"]:
            return (
                dbc.Alert(
                    f"文件处理失败: {stats_result['message']}",
                    color="danger",
                    className="mt-3",
                ),
                False,
            )

        # 第二步：执行实际查询
        result = query_ip_location(GLOBAL_SESSION, file_content)

        if result["success"]:
            return (
                dbc.Card(
                    [
                        dbc.CardHeader([html.H5("IP查询结果", className="mb-0")]),
                        dbc.CardBody(
                            [
                                dbc.Alert(
                                    f"查询完成！总计 {stats_result['total_count']} 个IP，预计用时 {stats_result['estimated_time']:.1f} 秒。{result['message']}",
                                    color="success",
                                    className="mb-2",
                                ),
                                html.Pre(
                                    result.get("raw_response", "无响应内容"),
                                    style={
                                        "white-space": "pre-wrap",
                                        "word-wrap": "break-word",
                                        "max-height": "400px",
                                        "overflow-y": "auto",
                                    },
                                ),
                            ]
                        ),
                    ],
                    className="mt-3",
                ),
                False,
            )
        else:
            return (
                dbc.Alert(
                    f"IP查询失败: {result['message']}",
                    color="danger",
                    className="mt-3",
                ),
                False,
            )

    except Exception as e:
        return (
            dbc.Alert(f"IP查询出错: {str(e)}", color="danger", className="mt-3"),
            False,
        )


# 文件下载回调
@app.callback(
    Output("download-data", "data"),
    [Input({"type": "download-btn", "filename": dash.dependencies.ALL}, "n_clicks")],
    prevent_initial_call=True,
)
def download_file(n_clicks_list):
    ctx = dash.callback_context
    if not ctx.triggered:
        return dash.no_update

    # 获取触发的按钮信息
    button_id = ctx.triggered[0]["prop_id"]
    if not button_id or button_id == ".n_clicks":
        return dash.no_update

    # 检查是否有实际的点击值
    triggered_value = ctx.triggered[0]["value"]
    if triggered_value is None or triggered_value == 0:
        return dash.no_update

    try:
        # 提取按钮ID中的文件名
        # 格式类似: {"index":0,"type":"download-btn","filename":"pin_20240101_120000.txt"}.n_clicks
        import re

        match = re.search(r'"filename":"([^"]+)"', button_id)
        if not match:
            print(f"无法从按钮ID中提取文件名: {button_id}")
            return dash.no_update

        filename = match.group(1)
        print(f"准备下载文件: {filename}")

        # 构建文件路径
        filepath = os.path.join("query_results", filename)

        if os.path.exists(filepath):
            return dcc.send_file(filepath)
        else:
            print(f"文件不存在: {filepath}")
            return dash.no_update

    except Exception as e:
        print(f"文件下载失败: {str(e)}")
        print(f"按钮ID: {button_id}")
        return dash.no_update


# 登录状态显示回调
@app.callback(Output("login-status-area", "children"), [Input("login-session", "data")])
def update_login_status(session_data):
    if session_data and session_data.get("success"):
        # 已登录状态
        return dbc.Alert(
            [
                html.H6("系统已登录", className="alert-heading mb-0"),
                html.Small("可以正常使用所有功能", className="text-muted"),
            ],
            color="success",
            className="mb-3",
        )
    else:
        # 登录失败状态
        error_message = (
            session_data.get("message", "未知错误")
            if session_data
            else "系统初始化失败"
        )
        return dbc.Alert(
            [
                html.H6("系统登录失败", className="alert-heading mb-0"),
                html.Small(f"错误信息: {error_message}", className="text-muted"),
                html.Hr(className="my-2"),
                html.Small("请联系管理员检查系统配置", className="text-muted"),
            ],
            color="danger",
            className="mb-3",
        )


# 手动登录功能已注释 - 现在使用自动登录
"""
# 登录回调
@app.callback(
    [Output("login-session", "data"), Output("login-result", "children")],
    [Input("login-btn", "n_clicks")],
    [
        dash.dependencies.State("login-username", "value"),
        dash.dependencies.State("login-password", "value"),
    ],
)
def handle_login(n_clicks, username, password):
    if n_clicks is None:
        return None, ""

    if not username or not password:
        return None, dbc.Alert("请输入用户名和密码", color="warning", className="mt-2")

    try:
        result = login_to_system(username, password)
        if result["success"]:
            return result, dbc.Alert("登录成功！", color="success", className="mt-2")
        else:
            return None, dbc.Alert(
                f"登录失败: {result['message']}", color="danger", className="mt-2"
            )
    except Exception as e:
        return None, dbc.Alert(f"登录出错: {str(e)}", color="danger", className="mt-2")


# 退出登录回调
@app.callback(
    Output("login-session", "data", allow_duplicate=True),
    [Input("logout-btn", "n_clicks")],
    prevent_initial_call=True,
)
def handle_logout(n_clicks):
    if n_clicks is None:
        return dash.no_update
    return None
"""


if __name__ == "__main__":
    app.run(debug=True)
